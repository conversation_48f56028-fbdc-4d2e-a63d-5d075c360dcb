"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { ArrowLeft, Plus, Trash2, Truck } from "lucide-react"
import { getProduk } from "@/lib/directus"
import { getProductionStock, getDistributionChannels, createDistribution } from "@/lib/production-api"
import type { Produk } from "@/lib/directus"
import type { ProductionStock, DistributionChannel, DistributionFormData } from "@/lib/production-types"

interface DistributionItem {
  id: string
  produk_id: string
  channel_id: string
  quantity: number
  notes: string
}

export default function AddDistributionPage() {
  const router = useRouter()
  const { toast } = useToast()
  
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [products, setProducts] = useState<Produk[]>([])
  const [productionStock, setProductionStock] = useState<ProductionStock[]>([])
  const [distributionChannels, setDistributionChannels] = useState<DistributionChannel[]>([])
  const [distributions, setDistributions] = useState<DistributionItem[]>([
    {
      id: crypto.randomUUID(),
      produk_id: "",
      channel_id: "",
      quantity: 0,
      notes: ""
    }
  ])

  useEffect(() => {
    async function fetchData() {
      try {
        const [productsData, stockData, channelsData] = await Promise.all([
          getProduk(),
          getProductionStock(),
          getDistributionChannels()
        ])

        setProducts(productsData)
        setProductionStock(stockData)
        setDistributionChannels(channelsData)

        console.log('📊 Loaded data:')
        console.log('Products:', productsData?.length || 0)
        console.log('Production Stock:', stockData?.length || 0, stockData)
        console.log('Distribution Channels:', channelsData?.length || 0)

        if (stockData?.length === 0) {
          console.log('⚠️ No production stock data loaded - using development mode with fallback stock')
        }
      } catch (error) {
        console.error("Error fetching data:", error)
        toast({
          title: "Error",
          description: "Failed to load data",
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [toast])

  const addDistributionItem = () => {
    setDistributions(prev => [...prev, {
      id: crypto.randomUUID(),
      produk_id: "",
      channel_id: "",
      quantity: 0,
      notes: ""
    }])
  }

  const removeDistributionItem = (id: string) => {
    if (distributions.length > 1) {
      setDistributions(prev => prev.filter(item => item.id !== id))
    }
  }

  const updateDistributionItem = (id: string, field: keyof DistributionItem, value: any) => {
    setDistributions(prev => prev.map(item =>
      item.id === id ? { ...item, [field]: value } : item
    ))
  }

  const getAvailableStock = (produk_id: string) => {
    const stock = productionStock.find(s => s.produk_id === produk_id)
    console.log(`🔍 Stock lookup for product ${produk_id}:`, stock)
    console.log(`📊 Production stock array length:`, productionStock.length)

    // If no production stock data is available, return a reasonable default for development
    if (productionStock.length === 0) {
      console.log(`⚠️ No production stock data available, using fallback stock of 100`)
      return 100 // Fallback stock for development
    }

    const availableQuantity = stock?.available_quantity || 0
    console.log(`📊 Available quantity:`, availableQuantity)
    return availableQuantity
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    e.stopPropagation()

    console.log('🚀 Form submitted, current distributions:', distributions)
    console.log('🚀 Event type:', e.type)
    console.log('🚀 Is submitting:', isSubmitting)

    // Validation
    const validDistributions = distributions.filter(d => d.produk_id && d.channel_id && d.quantity > 0)

    console.log('✅ Valid distributions:', validDistributions)

    if (validDistributions.length === 0) {
      console.log('❌ No valid distributions found')
      toast({
        title: "Validation Error",
        description: "Please add at least one valid distribution",
        variant: "destructive"
      })
      return
    }

    console.log('🔍 Proceeding with validation checks...')

    // Check stock availability
    console.log('🔍 Checking stock availability...')
    console.log('📊 Production stock data available:', productionStock.length > 0)

    // Skip stock validation if no production stock data is available (development mode)
    if (productionStock.length === 0) {
      console.log('⚠️ Skipping stock validation - no production stock data available (development mode)')
    } else {
      for (const dist of validDistributions) {
        const availableStock = getAvailableStock(dist.produk_id)
        console.log(`📦 Product ${dist.produk_id}: requested ${dist.quantity}, available ${availableStock}`)
        if (dist.quantity > availableStock) {
          const product = products.find(p => p.id === dist.produk_id)
          console.log('❌ Insufficient stock detected')
          toast({
            title: "Insufficient Stock",
            description: `Not enough stock for ${product?.nama_produk}. Available: ${availableStock}`,
            variant: "destructive"
          })
          return
        }
      }
    }

    console.log('✅ Stock validation passed, starting submission...')
    setIsSubmitting(true)

    try {
      console.log('🔄 Grouping distributions by product...')
      // Group distributions by product
      const distributionsByProduct = validDistributions.reduce((acc, dist) => {
        if (!acc[dist.produk_id]) {
          acc[dist.produk_id] = []
        }
        acc[dist.produk_id].push({
          channel_id: dist.channel_id,
          quantity: dist.quantity,
          notes: dist.notes
        })
        return acc
      }, {} as Record<string, any[]>)

      console.log('📦 Grouped distributions by product:', distributionsByProduct)
      console.log('🚀 Starting API calls...')

      // Create distributions for each product using API endpoint
      const results = []
      const entries = Object.entries(distributionsByProduct)
      console.log(`🔄 Processing ${entries.length} product groups...`)

      for (const [produk_id, distributions] of entries) {
        console.log(`\n🎯 Processing product ID: ${produk_id}`)
        const token = localStorage.getItem('directus_token')

        console.log('📤 Sending request for product:', produk_id, 'with distributions:', distributions)
        console.log('🔑 Token available:', !!token)
        console.log('🔑 Token length:', token?.length || 0)

        try {
          const response = await fetch('/api/production/distribution', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              produk_id,
              distributions
            })
          })

          console.log('Response status:', response.status)
          console.log('Response headers:', Object.fromEntries(response.headers.entries()))

          const responseText = await response.text()
          console.log('Raw response:', responseText)

          let result
          try {
            result = JSON.parse(responseText)
          } catch (parseError) {
            console.error('Failed to parse response as JSON:', parseError)
            throw new Error(`Invalid response format: ${responseText}`)
          }

          if (!response.ok) {
            console.error('API Error:', result)
            throw new Error(result.error || result.details || `HTTP ${response.status}: ${result.message || 'Failed to create distribution'}`)
          }

          console.log('API Result:', result)

          if (result.success && result.data) {
            results.push(...result.data)
          } else {
            console.warn('Unexpected response format:', result)
            throw new Error('Unexpected response format from server')
          }
        } catch (fetchError) {
          console.error('Fetch error:', fetchError)
          throw fetchError
        }
      }

      console.log('🎉 All distributions created successfully, total:', results.length)

      toast({
        title: "Success",
        description: `Created ${results.length} distribution(s) successfully`,
      })

      console.log('🔄 Redirecting to production page...')
      router.push('/production?tab=distribution')
    } catch (error) {
      console.error("❌ Error creating distributions:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create distributions",
        variant: "destructive"
      })
    } finally {
      console.log('🏁 Submission process completed, resetting loading state')
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Loading...</h2>
          <p className="text-muted-foreground">Please wait</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center space-x-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Create Distribution</h1>
          <p className="text-muted-foreground">
            Distribute products to various channels
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Truck className="h-5 w-5" />
              <span>Distribution Items</span>
            </CardTitle>
            <CardDescription>
              Add products and quantities to distribute
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {distributions.map((distribution, index) => (
              <div
                key={distribution.id}
                className="border rounded-lg p-4 space-y-4"
              >
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Distribution #{index + 1}</h4>
                  {distributions.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeDistributionItem(distribution.id)}
                      className="text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Product *</Label>
                    <Select
                      value={distribution.produk_id || undefined}
                      onValueChange={(value) => updateDistributionItem(distribution.id, 'produk_id', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select product" />
                      </SelectTrigger>
                      <SelectContent>
                        {products.map((product) => {
                          const availableStock = getAvailableStock(product.id)
                          return (
                            <SelectItem key={product.id} value={product.id}>
                              {product.nama_produk} (Available: {availableStock})
                            </SelectItem>
                          )
                        })}
                      </SelectContent>
                    </Select>
                    {distribution.produk_id && (
                      <div className="mt-2 p-2 bg-muted/50 rounded-md">
                        <p className="text-sm font-medium text-foreground">
                          ✅ Selected: {products.find(p => p.id === distribution.produk_id)?.nama_produk}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Distribution Channel *</Label>
                    <Select
                      value={distribution.channel_id || undefined}
                      onValueChange={(value) => updateDistributionItem(distribution.id, 'channel_id', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select channel" />
                      </SelectTrigger>
                      <SelectContent>
                        {distributionChannels.map((channel) => (
                          <SelectItem key={channel.id} value={channel.id}>
                            {channel.name} ({channel.type})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {distribution.channel_id && (
                      <div className="mt-2 p-2 bg-muted/50 rounded-md">
                        <p className="text-sm font-medium text-foreground">
                          ✅ Selected: {distributionChannels.find(c => c.id === distribution.channel_id)?.name}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Quantity *</Label>
                    <Input
                      type="number"
                      min="1"
                      value={distribution.quantity}
                      onChange={(e) => updateDistributionItem(distribution.id, 'quantity', parseInt(e.target.value) || 0)}
                      placeholder="0"
                    />
                    {distribution.produk_id && (
                      <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-950 rounded-md">
                        <p className="text-sm font-medium text-blue-700 dark:text-blue-300">
                          📦 Available Stock: {getAvailableStock(distribution.produk_id)}
                          {productionStock.length === 0 && (
                            <span className="ml-2 text-xs text-orange-600 dark:text-orange-400">
                              (Development Mode)
                            </span>
                          )}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Notes</Label>
                    <Textarea
                      value={distribution.notes}
                      onChange={(e) => updateDistributionItem(distribution.id, 'notes', e.target.value)}
                      placeholder="Additional notes..."
                      rows={2}
                    />
                  </div>
                </div>
              </div>
            ))}

            <div className="flex justify-center pt-4">
              <Button
                type="button"
                onClick={addDistributionItem}
                variant="outline"
                size="sm"
                className="w-full max-w-xs"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Distribution Item
              </Button>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Creating..." : "Create Distributions"}
          </Button>
        </div>
      </form>
    </div>
  )
}
