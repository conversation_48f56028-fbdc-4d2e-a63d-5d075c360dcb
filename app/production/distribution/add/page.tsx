"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { ArrowLeft, Plus, Trash2, Truck } from "lucide-react"
import { getProduk } from "@/lib/directus"
import { getProductionStock, getDistributionChannels, createDistribution } from "@/lib/production-api"
import type { Produk } from "@/lib/directus"
import type { ProductionStock, DistributionChannel, DistributionFormData } from "@/lib/production-types"

interface DistributionItem {
  id: string
  produk_id: string
  channel_id: string
  quantity: number
  notes: string
}

export default function AddDistributionPage() {
  const router = useRouter()
  const { toast } = useToast()
  
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [products, setProducts] = useState<Produk[]>([])
  const [productionStock, setProductionStock] = useState<ProductionStock[]>([])
  const [distributionChannels, setDistributionChannels] = useState<DistributionChannel[]>([])
  const [distributions, setDistributions] = useState<DistributionItem[]>([
    {
      id: crypto.randomUUID(),
      produk_id: "",
      channel_id: "",
      quantity: 0,
      notes: ""
    }
  ])

  useEffect(() => {
    async function fetchData() {
      try {
        const [productsData, stockData, channelsData] = await Promise.all([
          getProduk(),
          getProductionStock(),
          getDistributionChannels()
        ])

        setProducts(productsData)
        setProductionStock(stockData)
        setDistributionChannels(channelsData)
      } catch (error) {
        console.error("Error fetching data:", error)
        toast({
          title: "Error",
          description: "Failed to load data",
          variant: "destructive"
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [toast])

  const addDistributionItem = () => {
    setDistributions(prev => [...prev, {
      id: crypto.randomUUID(),
      produk_id: "",
      channel_id: "",
      quantity: 0,
      notes: ""
    }])
  }

  const removeDistributionItem = (id: string) => {
    if (distributions.length > 1) {
      setDistributions(prev => prev.filter(item => item.id !== id))
    }
  }

  const updateDistributionItem = (id: string, field: keyof DistributionItem, value: any) => {
    setDistributions(prev => prev.map(item =>
      item.id === id ? { ...item, [field]: value } : item
    ))
  }

  const getAvailableStock = (produk_id: string) => {
    const stock = productionStock.find(s => s.produk_id === produk_id)
    return stock?.available_quantity || 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    console.log('Form submitted, current distributions:', distributions)

    // Validation
    const validDistributions = distributions.filter(d => d.produk_id && d.channel_id && d.quantity > 0)

    console.log('Valid distributions:', validDistributions)

    if (validDistributions.length === 0) {
      toast({
        title: "Validation Error",
        description: "Please add at least one valid distribution",
        variant: "destructive"
      })
      return
    }

    // Check stock availability
    for (const dist of validDistributions) {
      const availableStock = getAvailableStock(dist.produk_id)
      if (dist.quantity > availableStock) {
        const product = products.find(p => p.id === dist.produk_id)
        toast({
          title: "Insufficient Stock",
          description: `Not enough stock for ${product?.nama_produk}. Available: ${availableStock}`,
          variant: "destructive"
        })
        return
      }
    }

    setIsSubmitting(true)

    try {
      // Group distributions by product
      const distributionsByProduct = validDistributions.reduce((acc, dist) => {
        if (!acc[dist.produk_id]) {
          acc[dist.produk_id] = []
        }
        acc[dist.produk_id].push({
          channel_id: dist.channel_id,
          quantity: dist.quantity,
          notes: dist.notes
        })
        return acc
      }, {} as Record<string, any[]>)

      console.log('Creating distributions:', distributionsByProduct)

      // Create distributions for each product using API endpoint
      const results = []
      for (const [produk_id, distributions] of Object.entries(distributionsByProduct)) {
        const token = localStorage.getItem('directus_token')

        console.log('Sending request for product:', produk_id, 'with distributions:', distributions)

        const response = await fetch('/api/production/distribution', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            produk_id,
            distributions
          })
        })

        console.log('Response status:', response.status)

        if (!response.ok) {
          const errorData = await response.json()
          console.error('API Error:', errorData)
          throw new Error(errorData.error || errorData.details || 'Failed to create distribution')
        }

        const result = await response.json()
        console.log('API Result:', result)
        results.push(...(result.data || []))
      }

      toast({
        title: "Success",
        description: `Created ${results.length} distribution(s) successfully`,
      })

      router.push('/production?tab=distribution')
    } catch (error) {
      console.error("Error creating distributions:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create distributions",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Loading...</h2>
          <p className="text-muted-foreground">Please wait</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center space-x-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Create Distribution</h1>
          <p className="text-muted-foreground">
            Distribute products to various channels
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Truck className="h-5 w-5" />
              <span>Distribution Items</span>
            </CardTitle>
            <CardDescription>
              Add products and quantities to distribute
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {distributions.map((distribution, index) => (
              <div
                key={distribution.id}
                className="border rounded-lg p-4 space-y-4"
              >
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Distribution #{index + 1}</h4>
                  {distributions.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeDistributionItem(distribution.id)}
                      className="text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Product *</Label>
                    <Select
                      value={distribution.produk_id}
                      onValueChange={(value) => updateDistributionItem(distribution.id, 'produk_id', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select product" />
                      </SelectTrigger>
                      <SelectContent>
                        {products.map((product) => {
                          const availableStock = getAvailableStock(product.id)
                          return (
                            <SelectItem key={product.id} value={product.id}>
                              {product.nama_produk} (Available: {availableStock})
                            </SelectItem>
                          )
                        })}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Distribution Channel *</Label>
                    <Select
                      value={distribution.channel_id}
                      onValueChange={(value) => updateDistributionItem(distribution.id, 'channel_id', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select channel" />
                      </SelectTrigger>
                      <SelectContent>
                        {distributionChannels.map((channel) => (
                          <SelectItem key={channel.id} value={channel.id}>
                            {channel.name} ({channel.type})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Quantity *</Label>
                    <Input
                      type="number"
                      min="1"
                      value={distribution.quantity}
                      onChange={(e) => updateDistributionItem(distribution.id, 'quantity', parseInt(e.target.value) || 0)}
                      placeholder="0"
                    />
                    {distribution.produk_id && (
                      <p className="text-sm text-muted-foreground">
                        Available: {getAvailableStock(distribution.produk_id)}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Notes</Label>
                    <Textarea
                      value={distribution.notes}
                      onChange={(e) => updateDistributionItem(distribution.id, 'notes', e.target.value)}
                      placeholder="Additional notes..."
                      rows={2}
                    />
                  </div>
                </div>
              </div>
            ))}

            <div className="flex justify-center pt-4">
              <Button
                type="button"
                onClick={addDistributionItem}
                variant="outline"
                size="sm"
                className="w-full max-w-xs"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Distribution Item
              </Button>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Creating..." : "Create Distributions"}
          </Button>
        </div>
      </form>
    </div>
  )
}
